class SupabaseConfig {
  // TODO: Replace these with your actual Supabase project credentials
  // Get these from your Supabase dashboard: Settings > API

  // 1. Go to https://supabase.com and create a new project
  // 2. Copy your Project URL (looks like: https://abcdefghijk.supabase.co)
  // 3. Copy your anon public key (long string starting with eyJhbGciOiJIUzI1NiIs...)
  // 4. Replace the values below:

  static const String supabaseUrl = 'https://ovmdhynseikzldfcjken.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92bWRoeW5zZWlremxkZmNqa2VuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwODEyODIsImV4cCI6MjA2MzY1NzI4Mn0.A5JRBfHvIFxX7WtCVUwFU_tcNeSle2Znhvesf3Q7il0';


}


