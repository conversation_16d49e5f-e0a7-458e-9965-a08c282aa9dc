{"buildFiles": ["C:\\Flutter SDK\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Android_Yassine\\freelancer_mobile\\freelancer_mobile\\android\\app\\.cxx\\Debug\\4t42d2x3\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Android_Yassine\\freelancer_mobile\\freelancer_mobile\\android\\app\\.cxx\\Debug\\4t42d2x3\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}