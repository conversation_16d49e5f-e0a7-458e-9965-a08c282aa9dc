{"buildFiles": ["C:\\Flutter SDK\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\xampp\\htdocs\\freelancer_mobile\\freelancer_mobile\\android\\app\\.cxx\\Debug\\592q6y3v\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\xampp\\htdocs\\freelancer_mobile\\freelancer_mobile\\android\\app\\.cxx\\Debug\\592q6y3v\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}