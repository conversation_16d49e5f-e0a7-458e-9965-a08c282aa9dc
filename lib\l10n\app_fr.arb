{"@@locale": "fr", "appTitle": "FreeLancer Mobile", "@appTitle": {"description": "The title of the application"}, "welcome": "Bienvenue", "@welcome": {"description": "Welcome message"}, "login": "Connexion", "@login": {"description": "Login button text"}, "logout": "Déconnexion", "@logout": {"description": "Logout button text"}, "dashboard": "Tableau de bord", "@dashboard": {"description": "Dashboard screen title"}, "settings": "Paramètres", "@settings": {"description": "Settings screen title"}, "projects": "Projets", "@projects": {"description": "Projects tab title"}, "clients": "Clients", "@clients": {"description": "Clients tab title"}, "payments": "Paiements", "@payments": {"description": "Payments tab title"}, "invoices": "Factures", "@invoices": {"description": "Invoices tab title"}, "expenses": "<PERSON>é<PERSON>ses", "@expenses": {"description": "Expenses tab title"}, "menu": "<PERSON><PERSON>", "@menu": {"description": "Menu tab title"}, "quickActions": "Actions rapides", "@quickActions": {"description": "Quick actions section title"}, "newProject": "Nouveau projet", "@newProject": {"description": "New project action"}, "addClient": "Ajouter un client", "@addClient": {"description": "Add client action"}, "recordPayment": "Enregistrer un paiement", "@recordPayment": {"description": "Record payment action"}, "createInvoice": "<PERSON><PERSON>er une facture", "@createInvoice": {"description": "Create invoice action"}, "recentActivities": "Activités récentes", "@recentActivities": {"description": "Recent activities section title"}, "upcomingDeadlines": "Échéances à venir", "@upcomingDeadlines": {"description": "Upcoming deadlines section title"}, "viewAll": "Voir tout", "@viewAll": {"description": "View all button text"}, "noRecentActivities": "Aucune activité récente", "@noRecentActivities": {"description": "Message when no recent activities"}, "noUpcomingDeadlines": "Aucune échéance à venir", "@noUpcomingDeadlines": {"description": "Message when no upcoming deadlines"}, "totalRevenue": "<PERSON><PERSON>re d'affaires total", "@totalRevenue": {"description": "Total revenue label"}, "activeProjects": "Projets actifs", "@activeProjects": {"description": "Active projects label"}, "pendingPayments": "Paiements en attente", "@pendingPayments": {"description": "Pending payments label"}, "thisMonth": "Ce mois-ci", "@thisMonth": {"description": "This month label"}, "language": "<PERSON><PERSON>", "@language": {"description": "Language setting label"}, "theme": "Thème", "@theme": {"description": "Theme setting label"}, "currency": "<PERSON><PERSON>", "@currency": {"description": "Currency setting label"}, "notifications": "Notifications", "@notifications": {"description": "Notifications setting label"}, "security": "Sécurité", "@security": {"description": "Security setting label"}, "about": "À propos", "@about": {"description": "About setting label"}, "cancel": "Annuler", "@cancel": {"description": "Cancel button text"}, "save": "Enregistrer", "@save": {"description": "Save button text"}, "edit": "Modifier", "@edit": {"description": "Edit button text"}, "delete": "<PERSON><PERSON><PERSON><PERSON>", "@delete": {"description": "Delete button text"}, "add": "Ajouter", "@add": {"description": "Add button text"}, "search": "<PERSON><PERSON><PERSON>", "@search": {"description": "Search placeholder text"}, "filter": "<PERSON><PERSON><PERSON>", "@filter": {"description": "Filter button text"}, "sort": "<PERSON><PERSON>", "@sort": {"description": "Sort button text"}, "refresh": "Actualiser", "@refresh": {"description": "Refresh button text"}, "account": "<PERSON><PERSON><PERSON>", "@account": {"description": "Account section title"}, "profileSettings": "Paramètres du profil", "@profileSettings": {"description": "Profile settings menu item"}, "updatePersonalInfo": "Mettre à jour vos informations personnelles", "@updatePersonalInfo": {"description": "Profile settings subtitle"}, "changePassword": "Changer le mot de passe", "@changePassword": {"description": "Change password menu item"}, "updateAccountPassword": "Mettre à jour le mot de passe de votre compte", "@updateAccountPassword": {"description": "Change password subtitle"}, "emailSettings": "Paramètres e-mail", "@emailSettings": {"description": "Email settings menu item"}, "manageEmailPreferences": "G<PERSON>rer les préférences e-mail", "@manageEmailPreferences": {"description": "Email settings subtitle"}, "application": "Application", "@application": {"description": "Application section title"}, "appearance": "Apparence", "@appearance": {"description": "Appearance menu item"}, "themeDisplaySettings": "Paramètres de thème et d'affichage", "@themeDisplaySettings": {"description": "Appearance subtitle"}, "dataStorage": "Données et stockage", "@dataStorage": {"description": "Data storage menu item"}, "backupSyncSettings": "Paramètres de sauvegarde et synchronisation", "@backupSyncSettings": {"description": "Data storage subtitle"}, "business": "Entreprise", "@business": {"description": "Business section title"}, "businessProfile": "Profil d'entreprise", "@businessProfile": {"description": "Business profile menu item"}, "companyInfoBranding": "Informations de l'entreprise et image de marque", "@companyInfoBranding": {"description": "Business profile subtitle"}, "invoiceSettings": "Paramètres de facturation", "@invoiceSettings": {"description": "Invoice settings menu item"}, "defaultInvoiceTemplates": "Modèles de factures et paramètres par défaut", "@defaultInvoiceTemplates": {"description": "Invoice settings subtitle"}, "taxSettings": "Paramètres fiscaux", "@taxSettings": {"description": "Tax settings menu item"}, "taxRatesCalculation": "Taux d'imposition et préférences de calcul", "@taxRatesCalculation": {"description": "Tax settings subtitle"}, "currencyRates": "Devises et taux", "@currencyRates": {"description": "Currency rates menu item"}, "supportInformation": "Support et informations", "@supportInformation": {"description": "Support section title"}, "helpSupport": "Aide et support", "@helpSupport": {"description": "Help support menu item"}, "getHelpContactSupport": "<PERSON><PERSON><PERSON><PERSON> de l'aide et contacter le support", "@getHelpContactSupport": {"description": "Help support subtitle"}, "termsPrivacy": "Conditions et confidentialité", "@termsPrivacy": {"description": "Terms privacy menu item"}, "termsServicePrivacyPolicy": "Conditions d'utilisation et politique de confidentialité", "@termsServicePrivacyPolicy": {"description": "Terms privacy subtitle"}, "appVersionInfo": "Version de l'application et informations", "@appVersionInfo": {"description": "About subtitle"}, "userName": "Nom d'utilisateur", "@userName": {"description": "Default user name"}, "userEmail": "<EMAIL>", "@userEmail": {"description": "Default user email"}, "freelancerAccount": "<PERSON><PERSON><PERSON> freelance", "@freelancerAccount": {"description": "Account type label"}, "signOut": "Se déconnecter", "@signOut": {"description": "Sign out button"}, "selectLanguage": "Sélectionner la langue", "@selectLanguage": {"description": "Language selection dialog title"}, "autoSync": "Synchronisation automatique", "@autoSync": {"description": "Auto sync setting"}, "autoSyncDescription": "Synchroniser automatiquement les données en ligne", "@autoSyncDescription": {"description": "Auto sync description"}, "databaseBackupRestore": "Sauvegarde et restauration de la base de données", "@databaseBackupRestore": {"description": "Database section title"}, "backup": "<PERSON><PERSON><PERSON><PERSON>", "@backup": {"description": "Backup button"}, "restore": "<PERSON><PERSON><PERSON>", "@restore": {"description": "Restore button"}, "settingsExportImport": "Export et import des paramètres", "@settingsExportImport": {"description": "Settings section title"}, "export": "Exporter", "@export": {"description": "Export button"}, "import": "Importer", "@import": {"description": "Import button"}, "resetDatabase": "Réinitialiser la base de données", "@resetDatabase": {"description": "Reset database button"}, "close": "<PERSON><PERSON><PERSON>", "@close": {"description": "Close button"}, "defaultCurrency": "<PERSON><PERSON> par défaut", "@defaultCurrency": {"description": "Default currency dialog title"}, "importSettings": "Importer les paramètres", "@importSettings": {"description": "Import settings dialog title"}, "pasteSettingsJson": "Coller le JSON des paramètres ici", "@pasteSettingsJson": {"description": "Settings import placeholder"}, "restoreDatabase": "Restaurer la base de données", "@restoreDatabase": {"description": "Restore database dialog title"}, "restoreCancelled": "Restauration annulée", "@restoreCancelled": {"description": "Rest<PERSON> cancelled message"}, "welcomeBack": "Bon retour", "@welcomeBack": {"description": "Login screen welcome message"}, "signInToContinue": "Connectez-vous pour continuer", "@signInToContinue": {"description": "Login screen subtitle"}, "createAccount": "<PERSON><PERSON><PERSON> un compte", "@createAccount": {"description": "Register screen title"}, "joinOurCommunity": "<PERSON><PERSON><PERSON><PERSON> notre communauté", "@joinOurCommunity": {"description": "Register screen subtitle"}, "fullName": "Nom complet", "@fullName": {"description": "Full name field label"}, "enterFullName": "Entrez votre nom complet", "@enterFullName": {"description": "Full name field placeholder"}, "email": "E-mail", "@email": {"description": "Email field label"}, "enterEmail": "Entrez votre e-mail", "@enterEmail": {"description": "Email field placeholder"}, "password": "Mot de passe", "@password": {"description": "Password field label"}, "enterPassword": "Entrez votre mot de passe", "@enterPassword": {"description": "Password field placeholder"}, "confirmPassword": "Confirmer le mot de passe", "@confirmPassword": {"description": "Confirm password field label"}, "confirmPasswordPlaceholder": "Confirmez votre mot de passe", "@confirmPasswordPlaceholder": {"description": "Confirm password field placeholder"}, "signUp": "S'inscrire", "@signUp": {"description": "Sign up button text"}, "dontHaveAccount": "Vous n'avez pas de compte ? ", "@dontHaveAccount": {"description": "Login screen sign up prompt"}, "alreadyHaveAccount": "Vous avez déjà un compte ? ", "@alreadyHaveAccount": {"description": "Register screen login prompt"}, "signIn": "Se connecter", "@signIn": {"description": "Sign in button text"}, "loading": "Chargement...", "@loading": {"description": "Loading message"}, "error": "<PERSON><PERSON><PERSON>", "@error": {"description": "Error title"}, "success": "Su<PERSON>ès", "@success": {"description": "Success title"}, "warning": "Avertissement", "@warning": {"description": "Warning title"}, "info": "Information", "@info": {"description": "Info title"}, "ok": "OK", "@ok": {"description": "OK button"}, "yes": "O<PERSON>", "@yes": {"description": "Yes button"}, "no": "Non", "@no": {"description": "No button"}, "confirm": "Confirmer", "@confirm": {"description": "Confirm button"}, "retry": "<PERSON><PERSON><PERSON><PERSON>", "@retry": {"description": "Retry button"}, "continueAction": "<PERSON><PERSON><PERSON>", "@continueAction": {"description": "Continue button"}, "back": "Retour", "@back": {"description": "Back button"}, "next": "Suivant", "@next": {"description": "Next button"}, "done": "<PERSON><PERSON><PERSON><PERSON>", "@done": {"description": "Done button"}, "apply": "Appliquer", "@apply": {"description": "Apply button"}, "clear": "<PERSON><PERSON><PERSON><PERSON>", "@clear": {"description": "Clear button"}, "reset": "Réinitialiser", "@reset": {"description": "Reset button"}, "submit": "So<PERSON><PERSON><PERSON>", "@submit": {"description": "Submit button"}, "send": "Envoyer", "@send": {"description": "Send button"}, "update": "Mettre à jour", "@update": {"description": "Update button"}, "create": "<PERSON><PERSON><PERSON>", "@create": {"description": "Create button"}, "remove": "<PERSON><PERSON><PERSON><PERSON>", "@remove": {"description": "Remove button"}, "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@select": {"description": "Select button"}, "choose": "Choi<PERSON>", "@choose": {"description": "Choose button"}, "browse": "Parcourir", "@browse": {"description": "Browse button"}, "upload": "Télécharger", "@upload": {"description": "Upload button"}, "download": "Télécharger", "@download": {"description": "Download button"}, "share": "Partager", "@share": {"description": "Share button"}, "copy": "<PERSON><PERSON><PERSON>", "@copy": {"description": "Copy button"}, "paste": "<PERSON><PERSON>", "@paste": {"description": "Paste button"}, "cut": "Couper", "@cut": {"description": "Cut button"}, "undo": "Annuler", "@undo": {"description": "Undo button"}, "redo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@redo": {"description": "Redo button"}, "noActivitiesYet": "Aucune activité pour le moment", "@noActivitiesYet": {"description": "Empty activities message"}, "noActivitiesMatchFilters": "Aucune activité ne correspond à vos filtres", "@noActivitiesMatchFilters": {"description": "No filtered activities message"}, "tryAdjustingFilters": "Essayez d'ajuster votre recherche ou vos filtres", "@tryAdjustingFilters": {"description": "Filter adjustment suggestion"}, "activitiesWillAppear": "Les activités apparaîtront ici au fur et à mesure que vous utilisez l'application", "@activitiesWillAppear": {"description": "Activities explanation message"}, "noDeadlinesFound": "<PERSON><PERSON>ne <PERSON>ché<PERSON> trouvée", "@noDeadlinesFound": {"description": "No deadlines message"}, "addProjectsInvoices": "Ajoutez des projets et des factures pour suivre les échéances", "@addProjectsInvoices": {"description": "Deadlines explanation message"}, "demo": "Démo", "@demo": {"description": "Demo badge text"}, "showingDemoDeadlines": "Affichage des échéances de démonstration. Ajoutez de vrais projets et factures pour suivre les échéances réelles.", "@showingDemoDeadlines": {"description": "Demo deadlines explanation"}, "reports": "Rapports", "@reports": {"description": "Reports screen title"}, "filters": "Filtres", "@filters": {"description": "Filters section title"}, "timePeriod": "Période", "@timePeriod": {"description": "Time period filter label"}, "advancedFilters": "Filtres avancés", "@advancedFilters": {"description": "Advanced filters section title"}, "client": "Client", "@client": {"description": "Client filter label"}, "project": "Projet", "@project": {"description": "Project filter label"}, "status": "Statut", "@status": {"description": "Status filter label"}, "amount": "<PERSON><PERSON>", "@amount": {"description": "Amount filter label"}, "min": "Min", "@min": {"description": "Minimum amount placeholder"}, "max": "Max", "@max": {"description": "Maximum amount placeholder"}, "all": "Tous", "@all": {"description": "All filter option"}, "completed": "<PERSON><PERSON><PERSON><PERSON>", "@completed": {"description": "Completed status"}, "inProgress": "En cours", "@inProgress": {"description": "In progress status"}, "notStarted": "Non commencé", "@notStarted": {"description": "Not started status"}, "pending": "En attente", "@pending": {"description": "Pending status"}, "customDateRange": "Plage de dates personnalisée", "@customDateRange": {"description": "Custom date range label"}, "selectDateRange": "Sélectionner une plage de dates", "@selectDateRange": {"description": "Date range picker placeholder"}, "markAsPaid": "Marquer comme payé", "@markAsPaid": {"description": "Mark as paid menu option"}, "createPayment": "C<PERSON>er un paiement", "@createPayment": {"description": "Create payment button"}, "paymentDetails": "Détails du paiement :", "@paymentDetails": {"description": "Payment details label"}, "creatingPayment": "Création du paiement...", "@creatingPayment": {"description": "Creating payment loading message"}, "paymentCreatedSuccessfully": "Paiement créé avec succès !", "@paymentCreatedSuccessfully": {"description": "Payment creation success message"}, "errorCreatingPayment": "<PERSON><PERSON>ur lors de la création du paiement", "@errorCreatingPayment": {"description": "Payment creation error message"}, "cannotMarkPaid": "Impossible de marquer le projet comme payé : Informations du projet ou du client manquantes", "@cannotMarkPaid": {"description": "Cannot mark paid error message"}, "projectFullyPaid": "Le projet est déjà entièrement payé", "@projectFullyPaid": {"description": "Project fully paid message"}, "createPaymentForRemaining": "<PERSON><PERSON>er un paiement pour le montant restant ?", "@createPaymentForRemaining": {"description": "Payment confirmation question"}, "languageChanged": "Langue changée en", "@languageChanged": {"description": "Language change success message prefix"}, "failedToLoadDashboard": "Échec du chargement du tableau de bord", "@failedToLoadDashboard": {"description": "Dashboard loading error message"}, "errorLoadingDashboard": "Erreur lors du chargement du tableau de bord", "@errorLoadingDashboard": {"description": "Dashboard loading error prefix"}, "errorRefreshingDashboard": "Erreur lors de l'actualisation du tableau de bord", "@errorRefreshingDashboard": {"description": "Dashboard refresh error prefix"}, "showingDemoData": "Affichage des données de démonstration. Commencez à ajouter des projets, des paiements et des factures pour voir les vraies activités.", "@showingDemoData": {"description": "Demo data explanation for activities"}, "thisWeek": "<PERSON><PERSON> se<PERSON>", "@thisWeek": {"description": "This week time period"}, "lastWeek": "La semaine dernière", "@lastWeek": {"description": "Last week time period"}, "last30Days": "Les 30 derniers jours", "@last30Days": {"description": "Last 30 days time period"}, "last90Days": "Les 90 derniers jours", "@last90Days": {"description": "Last 90 days time period"}, "thisYear": "<PERSON><PERSON> an<PERSON>", "@thisYear": {"description": "This year time period"}, "lastYear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@lastYear": {"description": "Last year time period"}, "totalEarnings": "<PERSON><PERSON><PERSON>", "@totalEarnings": {"description": "Total earnings label"}, "completedProjects": "Projets terminés", "@completedProjects": {"description": "Completed projects label"}, "unpaidProjects": "Projets impayés", "@unpaidProjects": {"description": "Unpaid projects label"}, "monthlyPerformance": "Performance mensuelle", "@monthlyPerformance": {"description": "Section ou titre affichant les performances du mois"}, "yearlyPerformance": "Performance annuelle", "@yearlyPerformance": {"description": "Section ou titre affichant les performances de l'année"}, "lastUpdated": "Dernière mise à jour", "@lastUpdated": {"description": "Étiquette indiquant la dernière mise à jour des données"}, "refreshDashboard": "Actualiser le tableau de bord", "@refreshDashboard": {"description": "Bouton ou action pour actualiser les données du tableau de bord"}, "profit": "Profit", "@profit": {"description": "Étiquette indiquant le profit financier"}, "revenue": "<PERSON><PERSON><PERSON>", "@revenue": {"description": "Étiquette montrant les revenus totaux avant dépenses"}, "active": "Actif", "@active": {"description": "Statut pour les éléments ou projets actifs"}, "netIncome": "Revenu net", "@netIncome": {"description": "Étiquette montrant le revenu après dépenses"}, "justNow": "À l’instant", "minutesAgo": "il y a {minutes} min", "hoursAgo": "il y a {hours} h", "daysAgo": "il y a {days} j", "currencyMillion": "{amount} M DA", "currencyThousand": "{amount} K DA", "currencyPlain": "{amount} DA", "overduePayment": "En retard de {days} jours", "dueInDays": "À payer dans {days} jours", "taxManagement": "Gestion des impôts", "taxYear": "Année fiscale :", "taxStatistics": "Statistiques fiscales", "paid": "<PERSON><PERSON>", "overdue": "En retard", "alerts": "<PERSON><PERSON><PERSON>", "taxPaymentsForYear": "Paiements d'impôts {year}", "noTaxesCalculated": "Aucun impôt calculé pour {year}", "calculateTaxesForYear": "Calculez vos impôts pour cette année", "calculateTaxes": "Calculer les impôts", "dueDate": "Échéance : {day}/{month}/{year}", "currencyWithSymbol": "{amount} DA", "@currencyWithSymbol": {"description": "Montant monétaire suivi du symbole de devise", "placeholders": {"amount": {}}}, "dueToday": "<PERSON><PERSON><PERSON><PERSON> aujou<PERSON>hui", "dueTomorrow": "Échéance demain", "overdueBy": "En retard de {days, plural, =1{# jour} other{# jours}}", "dueIn": "Due in {days, plural, =0{today} =1{{days} day} other{{days} days}}", "@dueIn": {"description": "Label for upcoming deadline in N days", "placeholders": {"days": {"type": "int"}}}, "businessManagement": "Gestion d'entreprise", "manageAllBusiness": "<PERSON><PERSON><PERSON> tous les aspects de votre activité freelance", "projectManagement": "Gestion de projet", "manageProjectsTrackProgress": "<PERSON><PERSON><PERSON> vos projets et suivez leur progression", "viewProjects": "Voir les projets", "addProject": "Ajouter un projet", "manageClients": "<PERSON><PERSON><PERSON> les clients", "trackPayments": "Suivre les paiements", "manageExpenses": "<PERSON><PERSON><PERSON> dépen<PERSON>", "createInvoices": "Créer des factures", "taxes": "<PERSON><PERSON><PERSON><PERSON>", "calendar": "<PERSON><PERSON><PERSON>", "viewEvents": "Voir les événements", "businessAnalytics": "Analy<PERSON> d'entreprise", "allDeadlines": "Toutes les échéances", "@allDeadlines": {"description": "Titre de section pour afficher toutes les échéances"}, "noDeadlinesWithFilter": "Aucune échéance pour {filter}", "@noDeadlinesWithFilter": {"description": "Message affiché quand aucune échéance n'est trouvée selon un filtre", "placeholders": {"filter": {"type": "String"}}}, "appInformation": "Informations sur l'application", "version": "Version", "buildNumber": "Numéro de <PERSON>", "releaseDate": "Date de publication", "platform": "Plateforme", "framework": "Framework", "database": "Base de données", "developer": "Développeur", "developerName": "Équipe Freelancer Mobile", "developerDescription": "Spécialisée dans le développement d'applications mobiles", "appDescription": "Conçue spécialement pour les freelances algériens afin de gérer efficacement leur activité avec conformité fiscale locale et prise en charge de la langue arabe.", "keyFeatures": "Fonctionnalités clés", "clientManagement": "Gestion des clients", "paymentTracking": "Suivi des paiements", "expenseManagement": "Gestion des dépenses", "invoiceGeneration": "Génération de factures", "algerianTaxManagement": "Gestion fiscale algérienne", "calendarEvents": "Calendrier et événements", "businessReports": "Rapports d'activité", "smartNotifications": "Notifications intelligentes", "legal": "Mentions légales", "termsOfService": "Conditions d'utilisation", "termsOfServiceDescription": "Lisez nos conditions générales", "privacyPolicy": "Politique de confidentialité", "privacyPolicyDescription": "Comment nous protégeons vos données", "openSourceLicenses": "Licences open source", "openSourceLicensesDescription": "Bibliothèques tierces et licences", "contactSupport": "Contact & Support", "emailSupport": "Support par email", "website": "Site web", "rateUs": "Noter l'application", "reportBug": "Signaler un bug", "rateUsDescription": "Notez l’application sur le Play Store", "reportBugDescription": "Aidez-nous à améliorer l'application", "invoice": "Facture", "tax": "<PERSON><PERSON><PERSON><PERSON>", "payment": "Paiement", "appPreferences": "Préférences de l'application", "@appPreferences": {"description": "Libellé pour les paramètres et la personnalisation de l'application"}, "editProject": "Modifier le projet", "projectName": "Nom du projet", "enterProjectName": "Entrez un nom descriptif", "description": "Description", "enterDescription": "D<PERSON><PERSON><PERSON>z le contenu du projet...", "selectClient": "Sélectionner un client", "noClientsFound": "Aucun client trouvé", "pleaseSelectClient": "Veuillez sélectionner un client", "pricingDetails": "Détails de tarification", "fixedPrice": "Prix fixe", "hourlyRate": "<PERSON><PERSON> horaire", "estimatedHours": "Heures estimées", "actualHours": "Heures réelles", "timelineStatus": "Calendrier et statut", "projectStatus": "Statut du projet", "projectTimeline": "<PERSON><PERSON><PERSON> du projet", "progress": "Avancement (%)", "startDate": "Date de début", "endDate": "Date de fin", "previous": "Précédent", "createProject": "Créer un projet", "updateProject": "Mettre à jour le projet", "enterFixedAmountError": "Veuillez entrer un montant fixe pour les projets à prix fixe", "fixedAmountGreaterThanZero": "Le montant fixe doit être supérieur à 0", "enterHourlyRateError": "Veuillez entrer un taux horaire pour les projets à taux horaire", "hourlyRateGreaterThanZero": "Le taux horaire doit être supérieur à 0", "freelanceManagementSlogan": "Solution complète de gestion freelance", "appInfo": "Informations de l'application", "endDateAfterStart": "La date de fin doit être après la date de début", "progressBetween0And100": "Le pourcentage d'avancement doit être entre 0 et 100", "validProgressRequired": "Veuillez entrer un pourcentage d'avancement valide", "projectUpdated": "Projet mis à jour avec succès", "projectCreated": "Projet créé avec succès", "projectSaveError": "Erreur lors de l'enregistrement du projet : {error}", "basicInfo": "Informations de base", "pricing": "Tarification", "timeline": "<PERSON><PERSON><PERSON>", "projectInfo": "Informations du projet", "projectDetailsHint": "Saisissez les détails de base de votre projet", "projectNameHint": "Entrez un nom de projet descriptif", "descriptionHint": "Décrivez ce que ce projet implique...", "tipProjectDescription": "Conseil : Utilisez un nom clair et une description détaillée pour suivre l'avancement de votre projet.", "clientSelectionHint": "Choisissez le client pour ce projet", "selectClientHint": "Sélectionnez un client pour ce projet", "clientSelected": "Client sélectionné", "clientCurrencyInfo": "La devise sera définie sur {currency}", "addClientInfo": "Vous devez ajouter au moins un client avant de créer un projet. Allez dans la gestion des clients pour en ajouter.", "pricingHint": "Définissez votre modèle de tarification et vos tarifs", "pricingModel": "Modèle de tarification", "oneTimePayment": "Paiement unique", "payPerHour": "Payer à l'heure", "fixedAmount": "Montant fixe", "totalProjectAmount": "Saisissez le montant total du projet", "hourlyRateHint": "Entrez votre taux horaire", "estimatedHoursHint": "Heures estimées", "actualHoursHint": "Heures réelles", "timelineHint": "Définissez le calendrier et le statut du projet", "progressHint": "Entrez un pourcentage de progression (0-100)", "startDateHint": "Sélectionnez la date de début", "endDateHint": "Sélectionnez la date de fin", "readyToCreate": "<PERSON><PERSON><PERSON><PERSON> à créer le projet", "reviewCreateInfo": "Vérifiez toutes les informations et cliquez sur \"Créer le projet\" pour l'ajouter à votre portefeuille.", "updating": "Mise à jour...", "creating": "Création...", "errorLoadingClients": "Erreur lors du chargement des clients : {error}", "clientNotFoundWarning": "Avertissement : le client d'origine est introuvable. Veuillez en sélectionner un.", "projectNameRequired": "Le nom du projet est requis", "descriptionRequired": "La description est requise", "hourlyRateRequired": "Le taux horaire est requis", "enterValidHourlyRate": "Veuillez entrer un taux horaire valide", "fixedAmountRequired": "Le montant fixe est requis", "enterValidAmount": "Veuillez entrer un montant valide", "enterValidHours": "Veuillez entrer un nombre d'heures valide", "paymentFilterFullyPaid": "Entièrement payé", "paymentFilterPartiallyPaid": "Partiellement payé", "paymentFilterUnpaid": "Non payé", "paymentFilterOverdue": "En retard", "noProjectsFound": "Aucun projet trouvé", "noProjectsYet": "Pas encore de projets", "adjustSearchOrFilters": "Essayez de modifier votre recherche ou vos filtres", "createFirstProject": "Créez votre premier projet pour commencer"}