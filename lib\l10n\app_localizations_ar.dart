// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'مستقل جوال';

  @override
  String get welcome => 'مرحبًا';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get dashboard => 'لوحة التحكم';

  @override
  String get settings => 'الإعدادات';

  @override
  String get projects => 'المشاريع';

  @override
  String get clients => 'العملاء';

  @override
  String get payments => 'المدفوعات';

  @override
  String get invoices => 'الفواتير';

  @override
  String get expenses => 'المصروفات';

  @override
  String get menu => 'القائمة';

  @override
  String get quickActions => 'إجراءات سريعة';

  @override
  String get newProject => 'مشروع جديد';

  @override
  String get addClient => 'إضافة عميل';

  @override
  String get recordPayment => 'تسجيل الدفع';

  @override
  String get createInvoice => 'إنشاء فاتورة';

  @override
  String get recentActivities => 'الأنشطة الأخيرة';

  @override
  String get upcomingDeadlines => 'المواعيد النهائية القادمة';

  @override
  String get viewAll => 'عرض الكل';

  @override
  String get noRecentActivities => 'لا توجد أنشطة حديثة';

  @override
  String get noUpcomingDeadlines => 'لا توجد مواعيد نهائية قادمة';

  @override
  String get totalRevenue => 'إجمالي الإيرادات';

  @override
  String get activeProjects => 'المشاريع النشطة';

  @override
  String get pendingPayments => 'المدفوعات المعلقة';

  @override
  String get thisMonth => 'هذا الشهر';

  @override
  String get language => 'اللغة';

  @override
  String get theme => 'المظهر';

  @override
  String get currency => 'العملة';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get security => 'الأمان';

  @override
  String get about => 'حول';

  @override
  String get cancel => 'إلغاء';

  @override
  String get save => 'حفظ';

  @override
  String get edit => 'تعديل';

  @override
  String get delete => 'حذف';

  @override
  String get add => 'إضافة';

  @override
  String get search => 'بحث';

  @override
  String get filter => 'تصفية';

  @override
  String get sort => 'فرز';

  @override
  String get refresh => 'تحديث';

  @override
  String get account => 'الحساب';

  @override
  String get profileSettings => 'إعدادات الملف الشخصي';

  @override
  String get updatePersonalInfo => 'تحديث معلوماتك الشخصية';

  @override
  String get changePassword => 'تغيير كلمة المرور';

  @override
  String get updateAccountPassword => 'تحديث كلمة مرور حسابك';

  @override
  String get emailSettings => 'إعدادات البريد الإلكتروني';

  @override
  String get manageEmailPreferences => 'إدارة تفضيلات البريد';

  @override
  String get application => 'التطبيق';

  @override
  String get appearance => 'المظهر';

  @override
  String get themeDisplaySettings => 'إعدادات المظهر والعرض';

  @override
  String get dataStorage => 'البيانات والتخزين';

  @override
  String get backupSyncSettings => 'إعدادات النسخ والنسخ الاحتياطي';

  @override
  String get business => 'الأعمال';

  @override
  String get businessProfile => 'ملف الأعمال';

  @override
  String get companyInfoBranding => 'معلومات الشركة والعلامة التجارية';

  @override
  String get invoiceSettings => 'إعدادات الفواتير';

  @override
  String get defaultInvoiceTemplates => 'قوالب وإعدادات الفواتير الافتراضية';

  @override
  String get taxSettings => 'إعدادات الضرائب';

  @override
  String get taxRatesCalculation => 'معدلات الضرائب وتفضيلات الحساب';

  @override
  String get currencyRates => 'العملات والأسعار';

  @override
  String get supportInformation => 'الدعم والمعلومات';

  @override
  String get helpSupport => 'المساعدة والدعم';

  @override
  String get getHelpContactSupport => 'الحصول على المساعدة واتصل بالدعم';

  @override
  String get termsPrivacy => 'الشروط والخصوصية';

  @override
  String get termsServicePrivacyPolicy => 'شروط الخدمة وسياسة الخصوصية';

  @override
  String get appVersionInfo => 'إصدار التطبيق والمعلومات';

  @override
  String get userName => 'اسم المستخدم';

  @override
  String get userEmail => '<EMAIL>';

  @override
  String get freelancerAccount => 'حساب مستقل';

  @override
  String get signOut => 'تسجيل الخروج';

  @override
  String get selectLanguage => 'اختر اللغة';

  @override
  String get autoSync => 'المزامنة التلقائية';

  @override
  String get autoSyncDescription =>
      'مزامنة البيانات تلقائيًا عند الاتصال بالإنترنت';

  @override
  String get databaseBackupRestore => 'نسخ احتياطي واستعادة قاعدة البيانات';

  @override
  String get backup => 'نسخ احتياطي';

  @override
  String get restore => 'استعادة';

  @override
  String get settingsExportImport => 'تصدير واستيراد الإعدادات';

  @override
  String get export => 'تصدير';

  @override
  String get import => 'استيراد';

  @override
  String get resetDatabase => 'إعادة تعيين قاعدة البيانات';

  @override
  String get close => 'إغلاق';

  @override
  String get defaultCurrency => 'العملة الافتراضية';

  @override
  String get importSettings => 'استيراد الإعدادات';

  @override
  String get pasteSettingsJson => 'الصق JSON الإعدادات هنا';

  @override
  String get restoreDatabase => 'استعادة قاعدة البيانات';

  @override
  String get restoreCancelled => 'تم إلغاء الاستعادة';

  @override
  String get welcomeBack => 'مرحبًا بعودتك';

  @override
  String get signInToContinue => 'سجّل الدخول للمتابعة';

  @override
  String get createAccount => 'إنشاء حساب';

  @override
  String get joinOurCommunity => 'انضم إلى مجتمعنا';

  @override
  String get fullName => 'الاسم الكامل';

  @override
  String get enterFullName => 'أدخل الاسم الكامل';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get enterEmail => 'أدخل البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get enterPassword => 'أدخل كلمة المرور';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get confirmPasswordPlaceholder => 'أكد كلمة المرور';

  @override
  String get signUp => 'إنشاء حساب';

  @override
  String get dontHaveAccount => 'لا تملك حسابًا؟ ';

  @override
  String get alreadyHaveAccount => 'هل لديك حساب بالفعل؟ ';

  @override
  String get signIn => 'تسجيل الدخول';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get error => 'خطأ';

  @override
  String get success => 'نجاح';

  @override
  String get warning => 'تحذير';

  @override
  String get info => 'معلومة';

  @override
  String get ok => 'حسنًا';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get confirm => 'تأكيد';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get continueAction => 'متابعة';

  @override
  String get back => 'رجوع';

  @override
  String get next => 'التالي';

  @override
  String get done => 'تم';

  @override
  String get apply => 'تطبيق';

  @override
  String get clear => 'مسح';

  @override
  String get reset => 'إعادة ضبط';

  @override
  String get submit => 'إرسال';

  @override
  String get send => 'إرسال';

  @override
  String get update => 'تحديث';

  @override
  String get create => 'إنشاء';

  @override
  String get remove => 'إزالة';

  @override
  String get select => 'اختيار';

  @override
  String get choose => 'اختيار';

  @override
  String get browse => 'تصفح';

  @override
  String get upload => 'تحميل';

  @override
  String get download => 'تنزيل';

  @override
  String get share => 'مشاركة';

  @override
  String get copy => 'نسخ';

  @override
  String get paste => 'لصق';

  @override
  String get cut => 'قص';

  @override
  String get undo => 'تراجع';

  @override
  String get redo => 'إعادة';

  @override
  String get noActivitiesYet => 'لا توجد أنشطة حتى الآن';

  @override
  String get noActivitiesMatchFilters => 'لا توجد أنشطة تطابق الفلاتر';

  @override
  String get tryAdjustingFilters => 'حاول تعديل البحث أو الفلاتر';

  @override
  String get activitiesWillAppear => 'الأنشطة ستظهر هنا أثناء استخدام التطبيق';

  @override
  String get noDeadlinesFound => 'لم تُعثر على مواعيد نهائية';

  @override
  String get addProjectsInvoices =>
      'أضف مشاريع وفواتير لتتبع المواعيد النهائية';

  @override
  String get demo => 'تجريبي';

  @override
  String get showingDemoDeadlines =>
      'عرض مواعيد نهائية تجريبية. أضف مشاريع وفواتير حقيقية لتتبع المواعيد الفعلية.';

  @override
  String get reports => 'التقارير';

  @override
  String get filters => 'فلاتر';

  @override
  String get timePeriod => 'الفترة الزمنية';

  @override
  String get advancedFilters => 'فلاتر متقدمة';

  @override
  String get client => 'العميل';

  @override
  String get project => 'المشروع';

  @override
  String get status => 'الحالة';

  @override
  String get amount => 'المبلغ';

  @override
  String get min => 'الحد الأدنى';

  @override
  String get max => 'الحد الأقصى';

  @override
  String get all => 'الكل';

  @override
  String get completed => 'منجز';

  @override
  String get inProgress => 'قيد التنفيذ';

  @override
  String get notStarted => 'لم يبدأ';

  @override
  String get pending => 'قيد الانتظار';

  @override
  String get customDateRange => 'نطاق تاريخ مخصص';

  @override
  String get selectDateRange => 'اختر نطاق تاريخ';

  @override
  String get markAsPaid => 'تحديد كمدفوع';

  @override
  String get createPayment => 'إنشاء دفعة';

  @override
  String get paymentDetails => 'تفاصيل الدفع:';

  @override
  String get creatingPayment => 'جارٍ إنشاء الدفع...';

  @override
  String get paymentCreatedSuccessfully => 'تم إنشاء الدفع بنجاح!';

  @override
  String get errorCreatingPayment => 'خطأ في إنشاء الدفع';

  @override
  String get cannotMarkPaid =>
      'لا يمكن تحديد المشروع على أنه مدفوع: معلومات المشروع أو العميل مفقودة';

  @override
  String get projectFullyPaid => 'المشروع مدفوع بالكامل بالفعل';

  @override
  String get createPaymentForRemaining => 'إنشاء دفعة للمبلغ المتبقي؟';

  @override
  String get languageChanged => 'تم تغيير اللغة إلى';

  @override
  String get failedToLoadDashboard => 'فشل في تحميل لوحة التحكم';

  @override
  String get errorLoadingDashboard => 'حدث خطأ أثناء تحميل لوحة التحكم';

  @override
  String get errorRefreshingDashboard => 'حدث خطأ أثناء تحديث لوحة التحكم';

  @override
  String get showingDemoData =>
      'عرض بيانات تجريبية. ابدأ بإضافة المشاريع والمدفوعات والفواتير لرؤية الأنشطة الفعلية.';

  @override
  String get thisWeek => 'هذا الأسبوع';

  @override
  String get lastWeek => 'الأسبوع الماضي';

  @override
  String get last30Days => 'آخر 30 يومًا';

  @override
  String get last90Days => 'آخر 90 يومًا';

  @override
  String get thisYear => 'هذا العام';

  @override
  String get lastYear => 'العام الماضي';

  @override
  String get totalEarnings => 'إجمالي الأرباح';

  @override
  String get completedProjects => 'المشاريع المكتملة';

  @override
  String get unpaidProjects => 'المشاريع غير المدفوعة';

  @override
  String get monthlyPerformance => 'الأداء الشهري';

  @override
  String get yearlyPerformance => 'الأداء السنوي';

  @override
  String get lastUpdated => 'آخر تحديث';

  @override
  String get refreshDashboard => 'تحديث لوحة التحكم';

  @override
  String get profit => 'الربح';

  @override
  String get revenue => 'الإيرادات';

  @override
  String get active => 'نشط';

  @override
  String get netIncome => 'صافي الدخل';

  @override
  String get justNow => 'الآن';

  @override
  String minutesAgo(Object minutes) {
    return 'قبل $minutes د';
  }

  @override
  String hoursAgo(Object hours) {
    return 'قبل $hours س';
  }

  @override
  String daysAgo(Object days) {
    return 'قبل $days ي';
  }

  @override
  String currencyMillion(Object amount) {
    return '$amount مليون د.ج';
  }

  @override
  String currencyThousand(Object amount) {
    return '$amount ألف د.ج';
  }

  @override
  String currencyPlain(Object amount) {
    return '$amount د.ج';
  }

  @override
  String overduePayment(Object days) {
    return 'متأخر منذ $days يوم';
  }

  @override
  String dueInDays(Object days) {
    return 'مستحق خلال $days يوم';
  }

  @override
  String get taxManagement => 'إدارة الضرائب';

  @override
  String get taxYear => 'السنة الضريبية:';

  @override
  String get taxStatistics => 'إحصائيات الضرائب';

  @override
  String get paid => 'مدفوع';

  @override
  String get overdue => 'متأخر';

  @override
  String get alerts => 'تنبيهات';

  @override
  String get calculateTaxesForYear => 'احسب ضرائبك لهذه السنة';

  @override
  String get calculateTaxes => 'احسب الضرائب';

  @override
  String taxPaymentsForYear(int year) {
    return 'مدفوعات الضرائب لعام $year';
  }

  @override
  String noTaxesCalculated(int year) {
    return 'لم يتم حساب الضرائب لعام $year';
  }

  @override
  String dueDate(Object day, Object month, Object year) {
    return 'تاريخ الاستحقاق: $day/$month/$year';
  }

  @override
  String currencyWithSymbol(Object amount) {
    return '$amount دج';
  }

  @override
  String get dueToday => 'مستحق اليوم';

  @override
  String get dueTomorrow => 'مستحق غدًا';

  @override
  String overdueBy(int days) {
    String _temp0 = intl.Intl.pluralLogic(
      days,
      locale: localeName,
      other: '# أيام',
      one: '# يوم',
    );
    return 'متأخر بمقدار $_temp0';
  }

  @override
  String dueIn(int days) {
    String _temp0 = intl.Intl.pluralLogic(
      days,
      locale: localeName,
      other: '$days أيام',
      one: '$days يوم',
      zero: 'اليوم',
    );
    return 'مستحق خلال $_temp0';
  }

  @override
  String get businessManagement => 'إدارة الأعمال';

  @override
  String get manageAllBusiness => 'إدارة جميع جوانب عملك الحر';

  @override
  String get projectManagement => 'إدارة المشاريع';

  @override
  String get manageProjectsTrackProgress => 'إدارة مشاريعك وتتبع التقدم';

  @override
  String get viewProjects => 'عرض المشاريع';

  @override
  String get addProject => 'إضافة مشروع';

  @override
  String get manageClients => 'إدارة العملاء';

  @override
  String get trackPayments => 'تتبع المدفوعات';

  @override
  String get manageExpenses => 'إدارة النفقات';

  @override
  String get createInvoices => 'إنشاء الفواتير';

  @override
  String get taxes => 'الضرائب';

  @override
  String get calendar => 'التقويم';

  @override
  String get viewEvents => 'عرض الأحداث';

  @override
  String get businessAnalytics => 'تحليلات الأعمال';

  @override
  String get allDeadlines => 'جميع المواعيد النهائية';

  @override
  String noDeadlinesWithFilter(String filter) {
    return 'لا توجد مواعيد نهائية لـ $filter';
  }

  @override
  String get appInformation => 'معلومات التطبيق';

  @override
  String get version => 'الإصدار';

  @override
  String get buildNumber => 'رقم البناء';

  @override
  String get releaseDate => 'تاريخ الإصدار';

  @override
  String get platform => 'المنصة';

  @override
  String get framework => 'الإطار';

  @override
  String get database => 'قاعدة البيانات';

  @override
  String get developer => 'المطور';

  @override
  String get developerName => 'فريق فريلانس موبايل';

  @override
  String get developerDescription => 'متخصص في تطوير تطبيقات الهاتف المحمول';

  @override
  String get appDescription =>
      'مصمم خصيصًا للمستقلين الجزائريين لإدارة أعمالهم بكفاءة مع التوافق الضريبي المحلي ودعم اللغة العربية.';

  @override
  String get keyFeatures => 'الميزات الرئيسية';

  @override
  String get clientManagement => 'إدارة العملاء';

  @override
  String get paymentTracking => 'تتبع المدفوعات';

  @override
  String get expenseManagement => 'إدارة المصاريف';

  @override
  String get invoiceGeneration => 'إنشاء الفواتير';

  @override
  String get algerianTaxManagement => 'إدارة الضرائب الجزائرية';

  @override
  String get calendarEvents => 'التقويم والفعاليات';

  @override
  String get businessReports => 'تقارير الأعمال';

  @override
  String get smartNotifications => 'إشعارات ذكية';

  @override
  String get legal => 'قانوني';

  @override
  String get termsOfService => 'شروط الخدمة';

  @override
  String get termsOfServiceDescription => 'اقرأ الشروط والأحكام الخاصة بنا';

  @override
  String get privacyPolicy => 'سياسة الخصوصية';

  @override
  String get privacyPolicyDescription => 'كيف نحمي بياناتك';

  @override
  String get openSourceLicenses => 'رخص المصادر المفتوحة';

  @override
  String get openSourceLicensesDescription => 'المكتبات والتراخيص الخارجية';

  @override
  String get contactSupport => 'الدعم والتواصل';

  @override
  String get emailSupport => 'الدعم عبر البريد الإلكتروني';

  @override
  String get website => 'الموقع الإلكتروني';

  @override
  String get rateUs => 'قيّمنا';

  @override
  String get reportBug => 'الإبلاغ عن خلل';

  @override
  String get rateUsDescription => 'قيّم التطبيق على متجر بلاي';

  @override
  String get reportBugDescription => 'ساعدنا في تحسين التطبيق';

  @override
  String get invoice => 'الفاتورة';

  @override
  String get tax => 'الضريبة';

  @override
  String get payment => 'الدفعة';

  @override
  String get appPreferences => 'تفضيلات التطبيق';

  @override
  String get editProject => 'تعديل المشروع';

  @override
  String get projectName => 'اسم المشروع';

  @override
  String get enterProjectName => 'أدخل اسمًا وصفيًا';

  @override
  String get description => 'الوصف';

  @override
  String get enterDescription => 'صف محتوى هذا المشروع...';

  @override
  String get selectClient => 'اختر العميل';

  @override
  String get noClientsFound => 'لم يتم العثور على عملاء';

  @override
  String get pleaseSelectClient => 'الرجاء اختيار عميل';

  @override
  String get pricingDetails => 'تفاصيل التسعير';

  @override
  String get fixedPrice => 'سعر ثابت';

  @override
  String get hourlyRate => 'سعر بالساعة';

  @override
  String get estimatedHours => 'الساعات المقدرة';

  @override
  String get actualHours => 'الساعات الفعلية';

  @override
  String get timelineStatus => 'الجدول الزمني والحالة';

  @override
  String get projectStatus => 'حالة المشروع';

  @override
  String get projectTimeline => 'الجدول الزمني للمشروع';

  @override
  String get progress => 'التقدم (%)';

  @override
  String get startDate => 'تاريخ البدء';

  @override
  String get endDate => 'تاريخ الانتهاء';

  @override
  String get previous => 'السابق';

  @override
  String get createProject => 'إنشاء مشروع';

  @override
  String get updateProject => 'تحديث المشروع';

  @override
  String get enterFixedAmountError =>
      'الرجاء إدخال مبلغ ثابت للمشاريع ذات السعر الثابت';

  @override
  String get fixedAmountGreaterThanZero =>
      'يجب أن يكون المبلغ الثابت أكبر من 0';

  @override
  String get enterHourlyRateError =>
      'الرجاء إدخال سعر بالساعة للمشاريع ذات التسعير بالساعة';

  @override
  String get hourlyRateGreaterThanZero => 'يجب أن يكون السعر بالساعة أكبر من 0';

  @override
  String get freelanceManagementSlogan => 'حل متكامل لإدارة العمل الحر';

  @override
  String get appInfo => 'معلومات التطبيق';

  @override
  String get endDateAfterStart => 'يجب أن يكون تاريخ الانتهاء بعد تاريخ البدء';

  @override
  String get progressBetween0And100 => 'يجب أن تكون نسبة التقدم بين 0 و 100';

  @override
  String get validProgressRequired => 'الرجاء إدخال نسبة تقدم صحيحة';

  @override
  String get projectUpdated => 'تم تحديث المشروع بنجاح';

  @override
  String get projectCreated => 'تم إنشاء المشروع بنجاح';

  @override
  String projectSaveError(Object error) {
    return 'خطأ أثناء حفظ المشروع: $error';
  }

  @override
  String get basicInfo => 'معلومات أساسية';

  @override
  String get pricing => 'التسعير';

  @override
  String get timeline => 'الجدول الزمني';

  @override
  String get projectInfo => 'معلومات المشروع';

  @override
  String get projectDetailsHint => 'أدخل التفاصيل الأساسية لمشروعك';

  @override
  String get projectNameHint => 'أدخل اسمًا وصفيًا للمشروع';

  @override
  String get descriptionHint => 'صف ما يتضمنه هذا المشروع...';

  @override
  String get tipProjectDescription =>
      'نصيحة: استخدم اسمًا واضحًا ووصفًا دقيقًا لتتبع تقدم مشروعك.';

  @override
  String get clientSelectionHint => 'اختر العميل لهذا المشروع';

  @override
  String get selectClientHint => 'اختر عميلاً لهذا المشروع';

  @override
  String get clientSelected => 'تم اختيار العميل';

  @override
  String clientCurrencyInfo(Object currency) {
    return 'سيتم تعيين العملة إلى $currency';
  }

  @override
  String get addClientInfo =>
      'يجب إضافة عميل واحد على الأقل قبل إنشاء مشروع. انتقل إلى إدارة العملاء لإضافتهم.';

  @override
  String get pricingHint => 'قم بتعيين نموذج التسعير والأسعار';

  @override
  String get pricingModel => 'نموذج التسعير';

  @override
  String get oneTimePayment => 'دفعة واحدة';

  @override
  String get payPerHour => 'الدفع لكل ساعة';

  @override
  String get fixedAmount => 'المبلغ الثابت';

  @override
  String get totalProjectAmount => 'أدخل المبلغ الإجمالي للمشروع';

  @override
  String get hourlyRateHint => 'أدخل السعر بالساعة';

  @override
  String get estimatedHoursHint => 'الساعات المقدرة';

  @override
  String get actualHoursHint => 'الساعات الفعلية';

  @override
  String get timelineHint => 'قم بتعيين الجدول الزمني للمشروع والحالة الحالية';

  @override
  String get progressHint => 'أدخل نسبة التقدم (0-100)';

  @override
  String get startDateHint => 'حدد تاريخ البدء';

  @override
  String get endDateHint => 'حدد تاريخ الانتهاء';

  @override
  String get readyToCreate => 'جاهز لإنشاء المشروع';

  @override
  String get reviewCreateInfo =>
      'راجع جميع المعلومات وانقر على \"إنشاء مشروع\" لإضافته إلى محفظتك.';

  @override
  String get updating => 'جارٍ التحديث...';

  @override
  String get creating => 'جارٍ الإنشاء...';

  @override
  String errorLoadingClients(Object error) {
    return 'خطأ في تحميل العملاء: $error';
  }

  @override
  String get clientNotFoundWarning =>
      'تحذير: لم يتم العثور على العميل الأصلي. الرجاء اختيار عميل.';

  @override
  String get projectNameRequired => 'اسم المشروع مطلوب';

  @override
  String get descriptionRequired => 'الوصف مطلوب';

  @override
  String get hourlyRateRequired => 'سعر الساعة مطلوب';

  @override
  String get enterValidHourlyRate => 'الرجاء إدخال سعر ساعة صالح';

  @override
  String get fixedAmountRequired => 'المبلغ الثابت مطلوب';

  @override
  String get enterValidAmount => 'الرجاء إدخال مبلغ صالح';

  @override
  String get enterValidHours => 'الرجاء إدخال عدد ساعات صالح';

  @override
  String get paymentFilterFullyPaid => 'مدفوع بالكامل';

  @override
  String get paymentFilterPartiallyPaid => 'مدفوع جزئياً';

  @override
  String get paymentFilterUnpaid => 'غير مدفوع';

  @override
  String get paymentFilterOverdue => 'متأخر عن الدفع';

  @override
  String get noProjectsFound => 'لم يتم العثور على مشاريع';

  @override
  String get noProjectsYet => 'لا توجد مشاريع بعد';

  @override
  String get adjustSearchOrFilters => 'حاول تعديل البحث أو الفلاتر';

  @override
  String get createFirstProject => 'أنشئ أول مشروع للبدء';
}
